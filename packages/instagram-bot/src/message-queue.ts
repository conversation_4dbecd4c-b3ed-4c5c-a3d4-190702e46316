import { processMessage } from './message-processor';
import { logError, ErrorContext } from './error-handler';
import { prisma } from '@workspace/database/client';

interface QueuedMessage {
  organizationId: string;
  senderId: string;
  messageId: string;
  messageText?: string;
  mediaUrl?: string;
  mediaType?: string;
  timestamp: number;
  timeoutId?: NodeJS.Timeout;
  conversationWindow?: number; // Window ID for grouping related messages
  priority?: number; // Higher priority for media messages
}

interface ConversationWindow {
  windowId: number;
  messages: QueuedMessage[];
  lastActivity: number;
  timeoutId?: NodeJS.Timeout;
}

// Map of senderId to conversation windows
const conversationWindows: Map<string, ConversationWindow> = new Map();

// Map of senderId to queued messages (legacy support)
const messageQueues: Map<string, QueuedMessage[]> = new Map();

// Map of senderId to timeout IDs (legacy support)
const processingTimeouts: Map<string, NodeJS.Timeout> = new Map();

// Configuration for conversation window detection
const CONVERSATION_WINDOW_TIMEOUT = 30000; // 30 seconds default
const MAX_MESSAGES_PER_WINDOW = 10; // Maximum messages to collect in one window

/**
 * Enhanced queue message function with conversation window support
 * @param message The message to queue
 * @param delayMs The delay in milliseconds before processing
 * @param useConversationWindows Whether to use enhanced conversation window logic
 * @param originalTimestamp Optional original timestamp from the webhook
 */
export async function queueMessage(
  message: Omit<QueuedMessage, 'timestamp' | 'timeoutId'>,
  delayMs: number,
  useConversationWindows: boolean = true,
  originalTimestamp?: number
): Promise<void> {
  const { organizationId, senderId } = message;

  try {
    if (useConversationWindows) {
      await queueMessageWithConversationWindow(message, delayMs, originalTimestamp);
    } else {
      await queueMessageLegacy(message, delayMs, originalTimestamp);
    }

    console.log(`Queued message from ${senderId}, will process after ${delayMs}ms delay`);
  } catch (error) {
    await logError(error as Error, {
      operation: 'queue_message',
      organizationId,
      messageId: message.messageId
    });

    // Fallback to legacy queueing if enhanced fails
    if (useConversationWindows) {
      console.log('Enhanced queueing failed, falling back to legacy method');
      await queueMessageLegacy(message, delayMs, originalTimestamp);
    } else {
      throw error;
    }
  }
}

/**
 * Enhanced message queueing with conversation window detection
 */
async function queueMessageWithConversationWindow(
  message: Omit<QueuedMessage, 'timestamp' | 'timeoutId'>,
  delayMs: number,
  originalTimestamp?: number
): Promise<void> {
  const { organizationId, senderId } = message;
  const now = Date.now();

  // Get or create conversation window for this sender
  let window = conversationWindows.get(senderId);

  // Determine message priority (media messages get higher priority)
  const priority = (message.mediaUrl || message.mediaType) ? 2 : 1;

  if (!window || (now - window.lastActivity) > CONVERSATION_WINDOW_TIMEOUT) {
    // Create new conversation window
    const windowId = now;
    window = {
      windowId,
      messages: [],
      lastActivity: now,
    };
    conversationWindows.set(senderId, window);
    console.log(`Created new conversation window ${windowId} for ${senderId}`);
  }

  // Clear existing timeout if any
  if (window.timeoutId) {
    clearTimeout(window.timeoutId);
  }

  // Add message to current window
  const queuedMessage: QueuedMessage = {
    ...message,
    timestamp: originalTimestamp || now,
    conversationWindow: window.windowId,
    priority
  };

  window.messages.push(queuedMessage);
  window.lastActivity = now;

  // Limit messages per window to prevent memory issues
  if (window.messages.length > MAX_MESSAGES_PER_WINDOW) {
    console.log(`Window ${window.windowId} reached max messages, processing immediately`);
    await processConversationWindow(organizationId, senderId);
    return;
  }

  // Set new timeout for processing this window
  const timeoutId = setTimeout(async () => {
    await processConversationWindow(organizationId, senderId);
  }, delayMs);

  window.timeoutId = timeoutId;

  console.log(`Added message to window ${window.windowId} (${window.messages.length} messages)`);
}

/**
 * Legacy message queueing for backward compatibility
 */
async function queueMessageLegacy(
  message: Omit<QueuedMessage, 'timestamp' | 'timeoutId'>,
  delayMs: number,
  originalTimestamp?: number
): Promise<void> {
  const { organizationId, senderId } = message;

  // Get or create queue for this sender
  let queue = messageQueues.get(senderId);
  if (!queue) {
    queue = [];
    messageQueues.set(senderId, queue);
  }

  // Add message to queue
  queue.push({
    ...message,
    timestamp: originalTimestamp || Date.now()
  });

  // Clear existing timeout if any
  const existingTimeout = processingTimeouts.get(senderId);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
  }

  // Set new timeout
  const timeoutId = setTimeout(async () => {
    await processQueuedMessages(organizationId, senderId);
  }, delayMs);

  // Store timeout ID
  processingTimeouts.set(senderId, timeoutId);
}

/**
 * Process conversation window with enhanced message grouping
 * @param organizationId The organization ID
 * @param senderId The sender ID
 */
async function processConversationWindow(organizationId: string, senderId: string): Promise<void> {
  try {
    const window = conversationWindows.get(senderId);
    if (!window || window.messages.length === 0) {
      console.log(`No messages in conversation window for ${senderId}`);
      return;
    }

    console.log(`[MESSAGE_QUEUE] Processing conversation window ${window.windowId} with ${window.messages.length} messages for ${senderId}`);
    console.log(`[MESSAGE_QUEUE] *** STARTING MESSAGE PROCESSING CYCLE ***`);

    // Check if contact is in terminal stage before processing
    const contact = await prisma.instagramContact.findFirst({
      where: {
        organizationId,
        instagramId: senderId
      },
      select: { id: true, stage: true }
    });

    if (contact) {
      const terminalStages = ['converted', 'disqualified', 'blocked', 'suspicious'];
      if (terminalStages.includes(contact.stage)) {
        console.log(`Contact ${contact.id} is in terminal stage ${contact.stage}, skipping conversation window processing`);

        // Clear the conversation window
        conversationWindows.delete(senderId);
        return;
      }
    }

    // Clear timeout
    if (window.timeoutId) {
      clearTimeout(window.timeoutId);
    }

    // Sort messages by priority (media messages first) then by timestamp
    const sortedMessages = window.messages.sort((a, b) => {
      if (a.priority !== b.priority) {
        return (b.priority || 0) - (a.priority || 0); // Higher priority first
      }
      return a.timestamp - b.timestamp; // Earlier messages first for same priority
    });

    // Process each message in the window
    // For now, we'll process the last message which includes conversation history
    // In the future, we could implement batch processing
    const lastMessage = sortedMessages[sortedMessages.length - 1];

    // Collect all media URLs and types from the window for comprehensive processing
    const mediaItems = sortedMessages
      .filter(msg => msg.mediaUrl || msg.mediaType)
      .map(msg => ({ url: msg.mediaUrl, type: msg.mediaType }));

    console.log(`Found ${mediaItems.length} media items in conversation window`);

    // Process the message with enhanced context
    await processMessage({
      organizationId,
      senderId,
      messageId: lastMessage.messageId,
      messageText: lastMessage.messageText,
      mediaUrl: lastMessage.mediaUrl,
      mediaType: lastMessage.mediaType,
      timestamp: new Date(lastMessage.timestamp)
    });

    // Clear the conversation window
    conversationWindows.delete(senderId);

    console.log(`[MESSAGE_QUEUE] Processed conversation window ${window.windowId} for ${senderId}`);
    console.log(`[MESSAGE_QUEUE] *** COMPLETED MESSAGE PROCESSING CYCLE ***`);
  } catch (error) {
    await logError(error as Error, {
      operation: 'process_conversation_window',
      organizationId,
      messageId: senderId // Using senderId as identifier since we're processing multiple messages
    });
    console.error(`[MESSAGE_QUEUE] Error processing conversation window for ${senderId}:`, error);
  }
}

/**
 * Process all queued messages for a sender (legacy method)
 * @param organizationId The organization ID
 * @param senderId The sender ID
 */
async function processQueuedMessages(organizationId: string, senderId: string): Promise<void> {
  try {
    // Get queue for this sender
    const queue = messageQueues.get(senderId);
    if (!queue || queue.length === 0) {
      console.log(`No messages in queue for ${senderId}`);
      return;
    }

    console.log(`Processing ${queue.length} queued messages for ${senderId}`);

    // Check if contact is in terminal stage before processing
    const contact = await prisma.instagramContact.findFirst({
      where: {
        organizationId,
        instagramId: senderId
      },
      select: { id: true, stage: true }
    });

    if (contact) {
      const terminalStages = ['converted', 'disqualified', 'blocked', 'suspicious'];
      if (terminalStages.includes(contact.stage)) {
        console.log(`Contact ${contact.id} is in terminal stage ${contact.stage}, skipping legacy queue processing`);

        // Clear the queue
        messageQueues.delete(senderId);
        processingTimeouts.delete(senderId);
        return;
      }
    }

    // Process the last message, which will include the entire conversation history
    const lastMessage = queue[queue.length - 1];

    // Process the message
    await processMessage({
      organizationId,
      senderId,
      messageId: lastMessage.messageId,
      messageText: lastMessage.messageText,
      mediaUrl: lastMessage.mediaUrl,
      mediaType: lastMessage.mediaType,
      timestamp: new Date(lastMessage.timestamp)
    });

    // Clear the queue
    messageQueues.delete(senderId);
    processingTimeouts.delete(senderId);

    console.log(`Processed queued messages for ${senderId}`);
  } catch (error) {
    await logError(error as Error, {
      operation: 'process_queued_messages',
      organizationId,
      messageId: senderId
    });
    console.error(`Error processing queued messages for ${senderId}:`, error);
  }
}

/**
 * Get conversation window statistics for monitoring
 * @param senderId Optional sender ID to get stats for specific sender
 */
export function getConversationWindowStats(senderId?: string): any {
  if (senderId) {
    const window = conversationWindows.get(senderId);
    return window ? {
      windowId: window.windowId,
      messageCount: window.messages.length,
      lastActivity: new Date(window.lastActivity).toISOString(),
      hasTimeout: !!window.timeoutId
    } : null;
  }

  // Return stats for all conversation windows
  const stats = Array.from(conversationWindows.entries()).map(([id, window]) => ({
    senderId: id,
    windowId: window.windowId,
    messageCount: window.messages.length,
    lastActivity: new Date(window.lastActivity).toISOString(),
    hasTimeout: !!window.timeoutId
  }));

  return {
    totalWindows: conversationWindows.size,
    windows: stats
  };
}

/**
 * Clear conversation window for a specific sender (for manual intervention)
 * @param senderId The sender ID
 */
export function clearConversationWindow(senderId: string): boolean {
  const window = conversationWindows.get(senderId);
  if (window) {
    if (window.timeoutId) {
      clearTimeout(window.timeoutId);
    }
    conversationWindows.delete(senderId);
    console.log(`Cleared conversation window for ${senderId}`);
    return true;
  }
  return false;
}

/**
 * Get the number of queued messages for a sender
 * @param senderId The sender ID
 */
export function getQueuedMessageCount(senderId: string): number {
  const queue = messageQueues.get(senderId);
  return queue ? queue.length : 0;
}

/**
 * Clear the queue for a sender
 * @param senderId The sender ID
 */
export function clearQueue(senderId: string): void {
  const existingTimeout = processingTimeouts.get(senderId);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
  }

  messageQueues.delete(senderId);
  processingTimeouts.delete(senderId);

  console.log(`Cleared queue for ${senderId}`);
}

#!/usr/bin/env node

/**
 * AI Orchestration Debug Monitor
 * 
 * This script helps monitor the logs to detect rapid-fire AI calls that cause
 * the "barrage of messages" issue. Run this alongside your application to
 * track AI orchestration patterns.
 * 
 * Usage:
 * 1. Run your application with logs piped to this script:
 *    npm run dev 2>&1 | node debug-ai-orchestration.js
 * 
 * 2. Or monitor existing log files:
 *    tail -f your-app.log | node debug-ai-orchestration.js
 * 
 * 3. Or run standalone and paste log lines manually
 */

const readline = require('readline');

// Track AI call sequences per conversation
const conversationTracking = new Map();

// Configuration
const RAPID_FIRE_THRESHOLD = 2; // Number of AI calls without user input to trigger warning
const TIME_WINDOW_MS = 60000; // 1 minute window to track calls

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  terminal: false
});

console.log('🔍 AI Orchestration Debug Monitor Started');
console.log('📝 Monitoring for rapid-fire AI calls...');
console.log('⚠️  Will warn when >1 AI call occurs without new user input');
console.log('🚨 Will error when >2 AI calls occur without new user input');
console.log('---');

function parseLogLine(line) {
  try {
    // Look for AI_ORCHESTRATION logs
    if (line.includes('[AI_ORCHESTRATION]')) {
      const match = line.match(/\[AI_ORCHESTRATION\]\s+(\w+):\s+({.*})/);
      if (match) {
        const event = match[1];
        const data = JSON.parse(match[2]);
        return { event, data, timestamp: new Date() };
      }
    }
    
    // Look for WEBHOOK logs
    if (line.includes('[WEBHOOK]')) {
      if (line.includes('Queueing message from')) {
        const senderMatch = line.match(/Queueing message from (\w+)/);
        const senderId = senderMatch ? senderMatch[1] : 'unknown';
        return { 
          event: 'WebhookMessageQueued', 
          data: { senderId }, 
          timestamp: new Date() 
        };
      }
    }
    
    // Look for MESSAGE_QUEUE logs
    if (line.includes('[MESSAGE_QUEUE]')) {
      if (line.includes('STARTING MESSAGE PROCESSING CYCLE')) {
        return { 
          event: 'MessageProcessingStarted', 
          data: {}, 
          timestamp: new Date() 
        };
      }
      if (line.includes('COMPLETED MESSAGE PROCESSING CYCLE')) {
        return { 
          event: 'MessageProcessingCompleted', 
          data: {}, 
          timestamp: new Date() 
        };
      }
    }
    
    // Look for MESSAGE_PROCESSOR logs
    if (line.includes('[MESSAGE_PROCESSOR]')) {
      if (line.includes('Triggering AI response generation')) {
        const orgMatch = line.match(/Organization: (\w+)/);
        const organizationId = orgMatch ? orgMatch[1] : 'unknown';
        return { 
          event: 'MessageProcessorTriggeringAI', 
          data: { organizationId }, 
          timestamp: new Date() 
        };
      }
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

function trackConversation(conversationId, event, data, timestamp) {
  if (!conversationTracking.has(conversationId)) {
    conversationTracking.set(conversationId, {
      aiCallCount: 0,
      lastUserMessage: null,
      lastAICallTime: null,
      events: []
    });
  }
  
  const tracking = conversationTracking.get(conversationId);
  tracking.events.push({ event, data, timestamp });
  
  // Keep only recent events (last 5 minutes)
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  tracking.events = tracking.events.filter(e => e.timestamp > fiveMinutesAgo);
  
  return tracking;
}

function analyzeAICall(conversationId, data, timestamp) {
  const tracking = trackConversation(conversationId, 'ClaudeAPICallTriggered', data, timestamp);
  
  // Check if this is a new user message or repeated call
  const isNewUserMessage = data.trigger === 'newUserMessage';
  
  if (isNewUserMessage) {
    // Reset count for new user message
    tracking.aiCallCount = 1;
    tracking.lastUserMessage = data.lastUserMessage;
    tracking.lastAICallTime = timestamp;
    
    console.log(`✅ [${conversationId}] New user message triggered AI call #${tracking.aiCallCount}`);
    if (data.lastUserMessage) {
      console.log(`   📝 User message: "${data.lastUserMessage.text.substring(0, 50)}..."`);
    }
  } else {
    // Increment count for repeated call
    tracking.aiCallCount++;
    tracking.lastAICallTime = timestamp;
    
    const level = tracking.aiCallCount === 2 ? '⚠️ ' : tracking.aiCallCount >= 3 ? '🚨' : '📊';
    const severity = tracking.aiCallCount === 2 ? 'WARNING' : tracking.aiCallCount >= 3 ? 'ERROR' : 'INFO';
    
    console.log(`${level} [${conversationId}] ${severity}: AI call #${tracking.aiCallCount} without new user input`);
    console.log(`   🔄 Trigger: ${data.trigger}`);
    console.log(`   ⏰ Time since last call: ${timestamp - tracking.lastAICallTime}ms`);
    
    if (tracking.lastUserMessage) {
      console.log(`   📝 Last user message: "${tracking.lastUserMessage.text.substring(0, 50)}..."`);
    }
    
    if (tracking.aiCallCount >= RAPID_FIRE_THRESHOLD) {
      console.log(`   🔥 RAPID-FIRE DETECTED: ${tracking.aiCallCount} AI calls for same user message!`);
      console.log(`   💡 This is likely causing the "barrage of messages" issue`);
      
      // Show recent events for this conversation
      console.log(`   📋 Recent events for this conversation:`);
      tracking.events.slice(-5).forEach((event, i) => {
        console.log(`      ${i + 1}. ${event.event} at ${event.timestamp.toISOString()}`);
      });
    }
  }
}

function analyzeEvent(parsedLog) {
  const { event, data, timestamp } = parsedLog;
  
  switch (event) {
    case 'ClaudeAPICallTriggered':
      if (data.conversationId) {
        analyzeAICall(data.conversationId, data, timestamp);
      }
      break;
      
    case 'ClaudeAPIResponseProcessed':
      if (data.conversationId) {
        console.log(`📤 [${data.conversationId}] Claude responded with ${data.claudeOutputMessagesCount} messages`);
      }
      break;
      
    case 'WebhookMessageQueued':
      console.log(`📨 Webhook queued message from ${data.senderId}`);
      break;
      
    case 'MessageProcessingStarted':
      console.log(`🚀 Message processing cycle started`);
      break;
      
    case 'MessageProcessingCompleted':
      console.log(`✅ Message processing cycle completed`);
      break;
      
    case 'MessageProcessorTriggeringAI':
      console.log(`🤖 Message processor triggering AI for org ${data.organizationId}`);
      break;
  }
}

// Process each line of input
rl.on('line', (line) => {
  const parsedLog = parseLogLine(line);
  if (parsedLog) {
    analyzeEvent(parsedLog);
  }
});

rl.on('close', () => {
  console.log('\n📊 Final Summary:');
  console.log(`Tracked ${conversationTracking.size} conversations`);
  
  for (const [conversationId, tracking] of conversationTracking.entries()) {
    if (tracking.aiCallCount > 1) {
      console.log(`🔥 ${conversationId}: ${tracking.aiCallCount} AI calls for same user message`);
    }
  }
  
  console.log('🔍 AI Orchestration Debug Monitor Stopped');
});

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n👋 Stopping monitor...');
  rl.close();
});

# AI Orchestration Debugging Guide

## Problem Summary

Your Instagram bot is experiencing a "barrage of messages" issue where users receive 8+ messages in rapid succession, even though <PERSON> is configured to send only 1-4 messages per API call. The issue is **not** with <PERSON> violating its constraints, but with your system's orchestration logic making multiple sequential API calls to <PERSON> without waiting for new user input.

## Root Cause Analysis

Based on your logs, the pattern is:
1. User sends message (e.g., "Nalogow brak")
2. System calls Claude API → <PERSON> responds with 1-4 messages
3. **System immediately calls Claude API again** with updated history (including <PERSON>'s own messages)
4. <PERSON> responds with another 1-4 messages
5. **System calls Claude API again** (loop continues)
6. Result: User receives 8+ messages that appear to be from a single AI response

## Logging Solution Implemented

I've added comprehensive logging throughout your codebase to track the exact flow and identify where the loop occurs:

### 1. AI Orchestration Logging (`packages/instagram-bot/src/ai-response.ts`)

**Added tracking for:**
- When Claude API calls are triggered
- Whether the trigger is a new user message or system re-evaluation
- Sequence counting per conversation
- Warning/Error levels for rapid-fire calls

**Key Log Patterns to Watch:**
```
[AI_ORCHESTRATION] ClaudeAPICallTriggered: {
  "aiCallSequenceInTurn": 1,
  "trigger": "newUserMessage",  // ✅ GOOD
  "lastUserMessage": { "text": "Nalogow brak" }
}

[AI_ORCHESTRATION] ClaudeAPICallTriggered: {
  "aiCallSequenceInTurn": 2,  // ⚠️ WARNING
  "trigger": "systemReevaluationAfterAIResponse",  // 🚨 PROBLEMATIC
  "lastUserMessage": { "text": "Nalogow brak" }  // Same user message!
}
```

### 2. Message Processing Logging (`packages/instagram-bot/src/message-processor.ts`)

**Added tracking for:**
- When message processor triggers AI calls
- User message content and conversation history length

### 3. Response Processing Logging (`packages/instagram-bot/src/ai-response.ts`)

**Added tracking for:**
- When response processing starts/completes
- Number of messages being sent
- End of processing cycles

### 4. Message Queue Logging (`packages/instagram-bot/src/message-queue.ts`)

**Added tracking for:**
- Message processing cycle start/completion
- Conversation window processing

### 5. Webhook Logging (`apps/dashboard/app/api/webhooks/instagram/route.ts`)

**Added tracking for:**
- When messages are received and queued
- Message content and organization details

## How to Use the Debugging Tools

### Option 1: Monitor Live Logs

Run your application and pipe logs to the debug monitor:

```bash
# Start your app with logs piped to the monitor
npm run dev 2>&1 | node debug-ai-orchestration.js
```

### Option 2: Monitor Existing Log Files

```bash
# Monitor existing log files
tail -f your-app.log | node debug-ai-orchestration.js
```

### Option 3: Manual Log Analysis

Look for these specific patterns in your logs:

**🚨 CRITICAL PATTERN - Rapid-Fire AI Calls:**
```
[AI_ORCHESTRATION] ClaudeAPICallTriggered: { "aiCallSequenceInTurn": 1, "trigger": "newUserMessage" }
[AI_ORCHESTRATION] ClaudeAPIResponseProcessed: { "claudeOutputMessagesCount": 4 }
[AI_ORCHESTRATION] ClaudeAPICallTriggered: { "aiCallSequenceInTurn": 2, "trigger": "systemReevaluationAfterAIResponse" }
[AI_ORCHESTRATION] ClaudeAPIResponseProcessed: { "claudeOutputMessagesCount": 3 }
[AI_ORCHESTRATION] ClaudeAPICallTriggered: { "aiCallSequenceInTurn": 3, "trigger": "systemReevaluationAfterAIResponse" }
```

**✅ NORMAL PATTERN - Single AI Call:**
```
[AI_ORCHESTRATION] ClaudeAPICallTriggered: { "aiCallSequenceInTurn": 1, "trigger": "newUserMessage" }
[AI_ORCHESTRATION] ClaudeAPIResponseProcessed: { "claudeOutputMessagesCount": 2 }
[AI_ORCHESTRATION] ProcessResponse completed
[AI_ORCHESTRATION] *** END OF AI RESPONSE PROCESSING CYCLE ***
```

## Expected Log Flow (Normal Operation)

1. `[WEBHOOK] Queueing message from {senderId}`
2. `[MESSAGE_QUEUE] *** STARTING MESSAGE PROCESSING CYCLE ***`
3. `[MESSAGE_PROCESSOR] Triggering AI response generation`
4. `[AI_ORCHESTRATION] ClaudeAPICallTriggered` (trigger: "newUserMessage")
5. `[AI_ORCHESTRATION] ClaudeAPIResponseProcessed`
6. `[AI_ORCHESTRATION] ProcessResponse started`
7. `[AI_ORCHESTRATION] ProcessResponse completed`
8. `[AI_ORCHESTRATION] *** END OF AI RESPONSE PROCESSING CYCLE ***`
9. `[MESSAGE_QUEUE] *** COMPLETED MESSAGE PROCESSING CYCLE ***`

## What to Look For

### 🚨 RED FLAGS (Indicates the Bug)

1. **Multiple `ClaudeAPICallTriggered` with same `lastUserMessage`**
2. **`trigger: "systemReevaluationAfterAIResponse"` without new user input**
3. **`aiCallSequenceInTurn` > 1 for same user message**
4. **Missing "END OF AI RESPONSE PROCESSING CYCLE" before next AI call**

### ✅ HEALTHY PATTERNS

1. **Single `ClaudeAPICallTriggered` per user message**
2. **`trigger: "newUserMessage"` for each AI call**
3. **Complete processing cycle before next user message**

## Next Steps

1. **Run the monitoring** to capture the exact moment the loop occurs
2. **Identify the trigger point** - what code path causes the second AI call
3. **Common suspects to investigate:**
   - Follow-up scheduling logic triggering immediate re-evaluation
   - Stage change logic triggering additional processing
   - Message sending completion handlers calling AI again
   - Conversation window logic processing messages multiple times

## Quick Fix Strategy

Once you identify the trigger point, the fix will likely involve:

1. **Adding state checks** to prevent AI calls when one is already in progress
2. **Ensuring complete processing cycles** before allowing new AI calls
3. **Separating user-triggered vs system-triggered AI calls**
4. **Adding cooldown periods** between AI calls for the same conversation

The logging will show you exactly where in your code the unwanted second/third AI calls are originating from.
